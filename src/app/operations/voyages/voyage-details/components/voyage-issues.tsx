import { useState, useEffect } from 'react';
import { LoaderCircle, Calendar, TriangleAlert } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';

interface IssuesData {
	row_number: number;
	Topic: number;
	Date: string;
	Description: string;
}

interface ApiResponse {
	issues: IssuesData[];
}

export default function VoyageIssues() {
	const [issuesData, setIssuesData] = useState<IssuesData[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchIssuesData = async () => {
			try {
				const response = await fetch('https://storage.googleapis.com/abx-workflows/latest/issues.json');
				if (!response.ok) {
					throw new Error('Failed to fetch issues data');
				}
				const data = (await response.json()) as ApiResponse[];
				setIssuesData(data[0]?.issues || []);
			} catch (err) {
				setError(err instanceof Error ? err.message : 'An error occurred');
			} finally {
				setLoading(false);
			}
		};

		void fetchIssuesData();
	}, []);

	if (loading) {
		return (
			<div className="grid gap-4 pt-8">
				<div className="flex justify-center">
					<LoaderCircle className="text-primary size-6 animate-spin" />
				</div>
				<div className="text-muted-foreground text-center text-sm">Loading issues data...</div>
			</div>
		);
	}

	if (error) {
		return <div className="grid gap-6 text-red-500">Error: {error}</div>;
	}

	return (
		<div className="flex flex-col gap-2 py-4">
			<div className="grid gap-2">
				<Accordion type="single" collapsible defaultValue="item-2" className="grid w-full gap-2">
					{issuesData.map(issue => (
						<AccordionItem
							key={issue.row_number}
							className="bg-panel/50 hover:bg-panel rounded-lg border px-4 shadow-xs"
							value={`item-${issue.row_number}`}
						>
							<AccordionTrigger className="flex items-center gap-3">
								<div className="flex flex-1 items-center gap-2">
									<TriangleAlert className="size-4 text-red-500" />
									<h3 className="text-md flex-1 font-semibold">{issue.Topic}</h3>
								</div>
								{issue.Date && (
									<div className="flex items-center gap-2">
										<Calendar className="text-muted-foreground size-4" />
										<div className="text-muted-foreground text-xs">{issue.Date}</div>
									</div>
								)}
							</AccordionTrigger>
							{issue.Description && (
								<AccordionContent className="ai-markdown text-md flex-1 text-justify leading-relaxed font-normal">
									<ReactMarkdown remarkPlugins={[remarkGfm]}>{issue.Description}</ReactMarkdown>
								</AccordionContent>
							)}
						</AccordionItem>
					))}
				</Accordion>
			</div>
		</div>
	);
}
