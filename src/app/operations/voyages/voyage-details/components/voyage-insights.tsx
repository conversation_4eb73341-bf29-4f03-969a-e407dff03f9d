import { useState, useEffect } from 'react';
import { LoaderCircle, Calendar } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';

interface InsightsData {
	row_number: number;
	date: string;
	topic: string;
	insight: string;
}

interface ApiResponse {
	insights: InsightsData[];
}

export default function VoyageInsights() {
	const [insightsData, setInsightsData] = useState<InsightsData[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchInsightsData = async () => {
			try {
				const response = await fetch('https://storage.googleapis.com/abx-workflows/latest/insights.json');
				if (!response.ok) {
					throw new Error('Failed to fetch insights data');
				}
				const data = (await response.json()) as ApiResponse[];
				setInsightsData(data[0]?.insights || []);
			} catch (err) {
				setError(err instanceof Error ? err.message : 'An error occurred');
			} finally {
				setLoading(false);
			}
		};

		void fetchInsightsData();
	}, []);

	if (loading) {
		return (
			<div className="grid gap-4 pt-8">
				<div className="flex justify-center">
					<LoaderCircle className="text-primary size-6 animate-spin" />
				</div>
				<div className="text-muted-foreground text-center text-sm">Loading insights data...</div>
			</div>
		);
	}

	if (error) {
		return <div className="grid gap-6 text-red-500">Error: {error}</div>;
	}

	return (
		<div className="flex flex-col gap-2 py-4">
			<div className="grid gap-2">
				<Accordion type="single" collapsible defaultValue="item-2" className="grid w-full gap-2">
					{insightsData.map(insight => (
						<AccordionItem
							key={insight.row_number}
							className="bg-panel/50 hover:bg-panel rounded-lg border px-4 shadow-xs"
							value={`item-${insight.row_number}`}
						>
							<AccordionTrigger className="flex items-center gap-3">
								{insight.topic && <h3 className="text-md flex-1 font-semibold">{insight.topic}</h3>}
								{insight.date && (
									<div className="flex items-center gap-2">
										<Calendar className="text-muted-foreground size-4" />
										<div className="text-muted-foreground text-xs">{insight.date}</div>
									</div>
								)}
							</AccordionTrigger>
							{insight.insight && (
								<AccordionContent className="ai-markdown text-md flex-1 text-justify leading-relaxed font-normal">
									<ReactMarkdown remarkPlugins={[remarkGfm]}>{insight.insight}</ReactMarkdown>
								</AccordionContent>
							)}
						</AccordionItem>
					))}
				</Accordion>
			</div>
		</div>
	);
}
