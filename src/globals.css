/*
	!!!WARNING!!!
	Changes to this file might cause regression in the Abraxa Hub host application.
	Since this file exposes global CSS's, it might cause issues with other pages that are rendered as micro-frontends in the Hub.
	Double check your selectors and use sparingly -- inline styles (CSS-in-JS) or Tailwind classes are viable alternatives.
	Use reserved class __ABRAXA_PROTOTYPE__ to define prototype-specific styles.
*/

@import 'tailwindcss';
@import url('https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Geist&family=Geist+Mono:wght@100..900&display=swap');

@custom-variant dark (&:is(.dark *));

@theme {
	--font-sans: 'Geist', sans-serif;
	--font-mono: 'Geist Mono', monospace;

	--text-sm: 0.8125rem;
	--text-sm--line-height: calc(1.25 / 0.8125);

	--text-md: 0.875rem;
	--text-md--line-height: 1.25;

	--border-width: 0.5px;

	--color-scrollbar: hsl(var(--scrollbar-background));
	--color-background: hsl(var(--background));
	--color-foreground: hsl(var(--foreground));

	--color-card: hsl(var(--card));
	--color-card-foreground: hsl(var(--card-foreground));

	--color-panel: hsl(var(--panel));

	--color-popover: hsl(var(--popover));
	--color-popover-foreground: hsl(var(--popover-foreground));

	--color-primary: hsl(var(--primary));
	--color-primary-foreground: hsl(var(--primary-foreground));

	--color-secondary: hsl(var(--secondary));
	--color-secondary-foreground: hsl(var(--secondary-foreground));

	--color-muted: hsl(var(--muted));
	--color-muted-foreground: hsl(var(--muted-foreground));

	--color-active: hsl(var(--active));
	--color-active-foreground: hsl(var(--active-foreground));

	--color-accent: hsl(var(--accent));
	--color-accent-foreground: hsl(var(--accent-foreground));

	--color-destructive: hsl(var(--destructive));
	--color-destructive-foreground: hsl(var(--destructive-foreground));

	--color-border: hsl(var(--border));
	--color-input: hsl(var(--input));
	--color-ring: hsl(var(--ring));

	--color-link: hsl(var(--link));

	--color-chart-1: hsl(var(--chart-1));
	--color-chart-2: hsl(var(--chart-2));
	--color-chart-3: hsl(var(--chart-3));
	--color-chart-4: hsl(var(--chart-4));
	--color-chart-5: hsl(var(--chart-5));
	--color-chart-6: hsl(var(--chart-6));

	--color-sidebar: hsl(var(--sidebar-background));
	--color-sidebar-foreground: hsl(var(--sidebar-foreground));
	--color-sidebar-primary: hsl(var(--sidebar-primary));
	--color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
	--color-sidebar-accent: hsl(var(--sidebar-accent));
	--color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
	--color-sidebar-border: hsl(var(--border));
	/* --color-sidebar-border: hsl(var(--sidebar-border)); */
	--color-sidebar-ring: hsl(var(--sidebar-ring));

	--radius-lg: var(--radius);
	--radius-md: calc(var(--radius) - 2px);
	--radius-sm: calc(var(--radius) - 4px);

	--animate-accordion-down: accordion-down 0.2s ease-out;
	--animate-accordion-up: accordion-up 0.2s ease-out;

	@keyframes accordion-down {
		from {
			height: 0;
		}
		to {
			height: var(--radix-accordion-content-height);
		}
	}
	@keyframes accordion-up {
		from {
			height: var(--radix-accordion-content-height);
		}
		to {
			height: 0;
		}
	}
}

@utility text-balance {
	text-wrap: balance;
}

@layer base {
	:root {
		/* --background: 0 0% 100%; */
		--font-geist-sans: 'GeistVF', sans-serif;
		--font-geist-mono: 'GeistMonoVF', monospace;
		--background: 0 0% 98.8%;
		--foreground: 222.2 84% 4.9%;
		--card: 0 0% 100%;
		--card-foreground: 222.2 84% 4.9%;
		--panel: 0 0% 100%;
		--popover: 0 0% 100%;
		--popover-foreground: 222.2 84% 4.9%;
		--active: 210 40% 96.1%;
		--active-foreground: 222.2 47.4% 11.2%;
		/* --primary: 222.2 47.4% 11.2%; */
		--primary: 229 65% 54%;
		--primary-foreground: 210 40% 98%;
		--secondary: 210 40% 96.1%;
		--secondary-foreground: 222.2 47.4% 11.2%;
		--muted: 210 40% 96.1%;
		--muted-foreground: 215.4 16.3% 46.9%;
		--accent: 210 40% 96.1%;
		--accent-foreground: 222.2 47.4% 11.2%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 210 40% 98%;
		--border: 214.3 31.8% 91.4%;
		--border-width: 0.5px;
		--input: 214.3 31.8% 91.4%;
		--link: 229 65% 54%;
		/* --ring: 222.2 84% 4.9%; */
		--ring: 229 65% 54%;
		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		/* --chart-3: 197 37% 24%; */
		--chart-3: 30 80% 55%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--chart-6: 27 87% 67%;
		--radius: 0.5rem;
		--scrollbar-background: hsl(215 20% 65%);
		--sidebar-background: 0 0% 98%;
		--sidebar-foreground: 240 5.3% 26.1%;
		/* --sidebar-primary: 240 5.9% 10%; */
		--sidebar-primary: 229 65% 54%;
		--sidebar-primary-foreground: 0 0% 98%;
		--sidebar-accent: 240 4.8% 95.9%;
		--sidebar-accent-foreground: 240 5.9% 10%;
		--sidebar-border: 220 13% 91%;
		--sidebar-ring: 217.2 91.2% 59.8%;
	}
	.dark {
		/* --background: 222.2 84% 4.9%; */
		--background: 222.2 47% 8.9%;
		--foreground: 236.05 20.05% 91.2%;
		/* --foreground: 210 40% 98%; */
		/* --card: 222.2 84% 4.9%; */
		--card: 222.2 47.37% 11.9%;
		--card-foreground: 210 40% 98%;
		--panel: 222.2 47.37% 11.9%;
		/* --popover: 222.2 84% 4.9%; */
		/* --popover: 222.2 47% 8.9%; */
		--popover: 222.2 47% 10.9%;
		--popover-foreground: 210 40% 98%;
		--active: 217.2 32.6% 17.5%;
		--active-foreground: 210 40% 98%;
		/* --primary: 210 40% 98%; */
		--primary: 229 65% 54%;
		/* --primary-foreground: 222.2 47.4% 11.2%; */
		--primary-foreground: 236.05 20.05% 91.2%;
		--secondary: 217.2 32.6% 17.5%;
		--secondary-foreground: 210 40% 98%;
		--muted: 217.2 32.6% 17.5%;
		--muted-foreground: 215 20.2% 65.1%;
		--accent: 217.2 32.6% 17.5% / 50%;
		--accent-foreground: 210 40% 98%;
		--destructive: 358.47 78.36% 58%;
		/* --destructive: 0 62.8% 30.6%; */
		--destructive-foreground: 210 40% 98%;
		--border: 217.2 32.6% 17.5%;
		--border-width: 0.5px;
		--input: 217.2 32.6% 17.5%;
		--link: 229.91 100% 82.68%;
		/* --ring: 212.7 26.8% 83.9%; */
		--ring: 229 65% 54%;
		--chart-1: 220 70% 50%;
		--chart-2: 160 60% 45%;
		--chart-3: 30 80% 55%;
		--chart-4: 280 65% 60%;
		--chart-5: 340 75% 55%;
		--chart-6: 357 96% 58%;
		--scrollbar-background: hsl(215 25% 27%);
		--sidebar-background: 222.2 84% 4.9%;
		/* --sidebar-background: 240 5.9% 10%; */
		--sidebar-foreground: 240 4.8% 95.9%;
		/* --sidebar-primary: 224.3 76.3% 48%; */
		--sidebar-primary: 229 65% 54%;
		--sidebar-primary-foreground: 0 0% 100%;
		/* --sidebar-accent: 240 3.7% 15.9%; */
		--sidebar-accent: 217.2 32.6% 17.5% / 50%;
		--sidebar-accent-foreground: 240 4.8% 95.9%;
		--sidebar-border: 240 3.7% 15.9%;
		--sidebar-ring: 217.2 91.2% 59.8%;
	}
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	.__ABRAXA_PROTOTYPE__ {
		*,
		::after,
		::before,
		::backdrop,
		::file-selector-button {
			border-color: hsl(var(--border, currentValue));
		}
	}

	.__ABRAXA_PROTOTYPE__.dark *,
	.dark .__ABRAXA_PROTOTYPE__ *,
	.dark .__ABRAXA_PROTOTYPE__ ::after,
	.dark .__ABRAXA_PROTOTYPE__ ::before,
	.dark .__ABRAXA_PROTOTYPE__ ::backdrop,
	.dark .__ABRAXA_PROTOTYPE__ ::file-selector-button {
		border-color: hsl(var(--border, currentValue));
	}
}

@layer base {
	.__ABRAXA_PROTOTYPE__ {
		@apply border-border;
		font-family: var(--font-sans);
	}
	.__ABRAXA_PROTOTYPE__ body {
		@apply bg-background text-foreground;
	}
}
@layer base {
	.__ABRAXA_PROTOTYPE__ {
		button,
		[role='button'] {
			cursor: pointer;
		}
		button:disabled,
		[role='button']:disabled {
			cursor: default;
		}
	}
}

@layer utilities {
	.__ABRAXA_PROTOTYPE__ {
		.border {
			border-width: var(--border-width);
		}
		.border-l {
			border-left-width: var(--border-width);
		}
		.border-r {
			border-right-width: var(--border-width);
		}
		.border-t {
			border-top-width: var(--border-width);
		}
		.border-b {
			border-bottom-width: var(--border-width);
		}
		.border-default {
			border-width: var(--border-width);
		}
		.border-none {
			border-style: none;
		}
		.border-0 {
			border-width: 0;
		}
	}
}

/* Scrollbar */
.__ABRAXA_PROTOTYPE__ {
	::-webkit-scrollbar {
		width: 6px;
		height: 6px;
	}
	::-webkit-scrollbar-track {
		background: transparent;
	}
	::-webkit-scrollbar-thumb {
		background-color: var(--scrollbar-background);
		border-radius: 6px;
	}
}

/* Dotted Background */
.__ABRAXA_PROTOTYPE__ {
	.bg-dotted-amber {
		background-image: radial-gradient(var(--color-amber-500) 1px, transparent 1px);
		background-size: 3px 1px;
	}
}

/* Popover Fix */
.j-full {
	width: var(--radix-popover-trigger-width);
	max-height: var(--radix-popover-content-available-height);
}
.j-normal {
	width: auto !important;
}

/* Data Table Header Button  */
.a-data-table-header-button[data-state='closed'] .lucide-chevrons-up-down {
	opacity: 0;
}
.a-data-table-header-button:hover .lucide-chevrons-up-down,
.a-data-table-header-button[data-state='open'] .lucide-chevrons-up-down {
	opacity: 1;
}
.text-2xs {
	font-size: 0.625rem;
}

/* Activity List */
.a-activity-list li:last-child .a-list-sep {
	display: none;
}

/* Dot Pulse */
.dot-pulse {
	position: relative;
	left: -9999px;
	width: 10px;
	height: 10px;
	border-radius: 5px;
	background-color: hsl(var(--primary));
	color: hsl(var(--primary));
	box-shadow: 9999px 0 0 -5px;
	animation: dot-pulse 1.5s infinite linear;
	animation-delay: 0.25s;
}
.dot-pulse::before,
.dot-pulse::after {
	content: '';
	display: inline-block;
	position: absolute;
	top: 0;
	width: 10px;
	height: 10px;
	border-radius: 5px;
	background-color: hsl(var(--primary));
	color: hsl(var(--primary));
}
.dot-pulse::before {
	box-shadow: 9984px 0 0 -5px;
	animation: dot-pulse-before 1.5s infinite linear;
	animation-delay: 0s;
}
.dot-pulse::after {
	box-shadow: 10014px 0 0 -5px;
	animation: dot-pulse-after 1.5s infinite linear;
	animation-delay: 0.5s;
}

@keyframes dot-pulse-before {
	0% {
		box-shadow: 9984px 0 0 -5px;
	}
	30% {
		box-shadow: 9984px 0 0 2px;
	}
	60%,
	100% {
		box-shadow: 9984px 0 0 -5px;
	}
}
@keyframes dot-pulse {
	0% {
		box-shadow: 9999px 0 0 -5px;
	}
	30% {
		box-shadow: 9999px 0 0 2px;
	}
	60%,
	100% {
		box-shadow: 9999px 0 0 -5px;
	}
}
@keyframes dot-pulse-after {
	0% {
		box-shadow: 10014px 0 0 -5px;
	}
	30% {
		box-shadow: 10014px 0 0 2px;
	}
	60%,
	100% {
		box-shadow: 10014px 0 0 -5px;
	}
}

/* AI Markdown */
.ai-markdown,
.ai-markdown ol,
.ai-markdown ul {
	width: 100%;
	display: flex;
	flex-direction: column;
}
.ai-markdown ol,
.ai-markdown ul {
	padding-left: calc(var(--spacing) * 8);
	gap: calc(var(--spacing) * 4);
	margin: calc(var(--spacing) * 2) 0;
}
.ai-markdown ul {
	list-style: disc;
}
.ai-markdown ol {
	list-style: decimal;
}
.ai-markdown a {
	color: hsl(var(--link));
}
.ai-markdown h3 {
	font-size: var(--text-base);
	font-weight: var(--font-weight-bold);
}
.ai-markdown pre {
	padding: calc(var(--spacing) * 4);
	background: hsl(var(--panel));
	border-radius: var(--radius);
	border: 1px solid hsl(var(--border));
	font-size: var(--text-sm);
}
.ai-markdown table {
	border-spacing: 0;
	border-collapse: collapse;
	width: 100%;
	max-width: 100%;
	overflow: auto;
}
.ai-markdown table tr {
	border-top: 1px solid hsl(var(--border));
}
.ai-markdown table th,
.ai-markdown table td {
	padding: calc(var(--spacing) * 2);
	border: 1px solid hsl(var(--border));
}
.ai-markdown blockquote {
	margin: calc(var(--spacing) * 4) 0;
	padding: 0 calc(var(--spacing) * 4);
	border-left: 3px solid hsl(var(--border));
	text-align: left;
}
.ai-markdown blockquote ul,
.ai-markdown blockquote ol {
	margin: 0;
	padding-left: 0;
	list-style: none;
}
.ai-markdown p + p {
	margin-top: calc(var(--spacing) * 2);
}
pre,
code,
.language-html {
	white-space: pre-line;
	font-family: inherit;
	width: 100%;
}

.ai-markdown hr {
	border: none;
	border-top: 1px solid hsl(var(--border));
	margin: calc(var(--spacing) * 4) 0;
}
/*
  ---break---
*/
@layer base {
	.__ABRAXA_PROTOTYPE__ {
		@apply border-border outline-ring/50;
	}
	.__ABRAXA_PROTOTYPE__ body {
		overflow-x: hidden;
		@apply bg-background text-foreground;
	}
}
.lucide.size-3 {
	width: calc(var(--spacing) * 3) !important;
	height: calc(var(--spacing) * 3) !important;
}
.lucide.size-3\.5 {
	width: calc(var(--spacing) * 3.5) !important;
	height: calc(var(--spacing) * 3.5) !important;
}

.a-mask-image {
	mask-image: linear-gradient(180deg, #000 35%, transparent);
}
/* Datetime Field */
.a-datetime-filed {
	display: flex;
	align-items: center;
}
.a-datetime-filed button + button {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
	margin-left: -1px;
	border-left: 0;
}
.a-datetime-filed button + button > div {
	text-align: right;
}

/* Quantity Field */
.a-quantity-filed {
	display: flex;
	align-items: center;
}
.a-quantity-filed input {
	border-right: 0;
}
.a-quantity-filed button {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
	margin-left: -1px;
	border-left: 0;
}

.__ABRAXA_PROTOTYPE__ {
	/* Number Input */
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none;
		margin: 0;
	}
	input[type='number'] {
		-moz-appearance: textfield;
	}
}

/* Mapbox Popup */
.mapboxgl-popup .mapboxgl-popup-content {
	background: hsl(var(--background));
	color: hsl(var(--foreground));
	border: 1px solid hsl(var(--border));
	border-radius: var(--radius-xl);
	padding: calc(var(--spacing) * 4);
}
.mapboxgl-popup .mapboxgl-popup-close-button {
	width: 2rem;
	height: 2rem;
	color: hsl(var(--muted-foreground));
}
.mapboxgl-popup.mapboxgl-popup-anchor-bottom .mapboxgl-popup-tip {
	border-top-color: hsl(var(--background));
	margin-top: -1px;
}
.mapboxgl-ctrl-bottom-right,
.mapboxgl-ctrl-bottom-left {
	display: none;
}

/* Sidebar Fix */
.__ABRAXA_PROTOTYPE__ {
	.sidebar-b-l-0 [data-sidebar='sidebar'] {
		border-left: 0;
	}

	.flex-0 {
		flex: 0 !important;
	}
	[data-variant='absolute'] {
		position: absolute;
	}
}

/* Light Fill Fix */
.light\:fill-primary {
	&:is(.light *) {
		fill: var(--color-primary);
	}
}
